# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
plt.style.use('default')
sns.set_palette("husl")

# Import machine learning libraries
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import SimpleImputer
from sklearn.ensemble import RandomForestRegressor
import lightgbm as lgb
from catboost import CatBoostRegressor, Pool

# Download and load the dataset
import kagglehub
path = kagglehub.dataset_download("ananaymital/us-used-cars-dataset")
print("Dataset downloaded to:", path)

# Define optimized data types for memory efficiency
data_types = {
    'vin': 'object',
    'body_type': 'category',
    'city': 'category', 
    'city_fuel_economy': 'float32',
    'combine_fuel_economy': 'float32',
    'daysonmarket': 'int16',
    'engine_cylinders': 'category',
    'engine_displacement': 'float32',
    'exterior_color': 'category',
    'franchise_dealer': 'bool',
    'highway_fuel_economy': 'float32',
    'horsepower': 'float32',
    'is_certified': 'float32',
    'is_new': 'bool',
    'latitude': 'float32',
    'listing_id': 'int64',
    'longitude': 'float32',
    'mileage': 'float32',
    'owner_count': 'float32',
    'price': 'float32',
    'savings_amount': 'int64',
    'seller_rating': 'float32',
    'sp_id': 'float32',
    'vehicle_damage_category': 'float32',
    'year': 'int32'
}

# Load the dataset with optimized data types
df_path = path + "/used_cars_data.csv"
cars_df = pd.read_csv(df_path, low_memory=False)
print(f"Dataset loaded successfully!")
print(f"Shape: {cars_df.shape}")
print(f"Memory usage: {cars_df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")

# Display basic information about the dataset
cars_df.info()

# Display first few rows
cars_df.head()

# Check for missing values
missing_data = cars_df.isnull().sum()
missing_percent = (missing_data / len(cars_df)) * 100
missing_df = pd.DataFrame({
    'Missing_Count': missing_data,
    'Missing_Percentage': missing_percent
}).sort_values('Missing_Percentage', ascending=False)

print("Missing Data Summary:")
print(missing_df[missing_df['Missing_Count'] > 0].head(20))

# Descriptive statistics for numerical variables
numerical_cols = cars_df.select_dtypes(include=[np.number]).columns
cars_df[numerical_cols].describe()

# Check target variable distribution
plt.figure(figsize=(15, 5))

plt.subplot(1, 3, 1)
plt.hist(cars_df['price'].dropna(), bins=50, alpha=0.7, color='skyblue')
plt.title('Price Distribution')
plt.xlabel('Price ($)')
plt.ylabel('Frequency')

plt.subplot(1, 3, 2)
plt.hist(np.log1p(cars_df['price'].dropna()), bins=50, alpha=0.7, color='lightgreen')
plt.title('Log-transformed Price Distribution')
plt.xlabel('Log(Price + 1)')
plt.ylabel('Frequency')

plt.subplot(1, 3, 3)
plt.boxplot(cars_df['price'].dropna())
plt.title('Price Boxplot')
plt.ylabel('Price ($)')

plt.tight_layout()
plt.show()

print(f"Price statistics:")
print(f"Mean: ${cars_df['price'].mean():.2f}")
print(f"Median: ${cars_df['price'].median():.2f}")
print(f"Min: ${cars_df['price'].min():.2f}")
print(f"Max: ${cars_df['price'].max():.2f}")

# Analyze categorical variables - unique value counts
categorical_cols = ['body_type', 'city', 'engine_cylinders', 'exterior_color', 'make_name', 'model_name', 'fuel_type', 'transmission']

for col in categorical_cols:
    if col in cars_df.columns:
        unique_count = cars_df[col].nunique()
        print(f"\n{col.upper()}:")
        print(f"Unique values: {unique_count}")
        if unique_count <= 20:
            print(cars_df[col].value_counts().head(10))
        else:
            print("Top 10 most frequent values:")
            print(cars_df[col].value_counts().head(10))

# Remove rows with missing target variable (price)
print(f"Original dataset size: {len(cars_df)}")
cars_df = cars_df.dropna(subset=['price'])
print(f"After removing missing prices: {len(cars_df)}")

# Remove extreme price outliers (likely data entry errors)
Q1 = cars_df['price'].quantile(0.01)
Q99 = cars_df['price'].quantile(0.99)
print(f"Price range (1st-99th percentile): ${Q1:.2f} - ${Q99:.2f}")

cars_df = cars_df[(cars_df['price'] >= Q1) & (cars_df['price'] <= Q99)]
print(f"After removing price outliers: {len(cars_df)}")

# Remove columns with >70% missing data
missing_threshold = 0.7
cols_to_drop = missing_df[missing_df['Missing_Percentage'] > missing_threshold * 100].index.tolist()
print(f"Columns to drop (>70% missing): {cols_to_drop}")

cars_df = cars_df.drop(columns=cols_to_drop)
print(f"Remaining columns: {cars_df.shape[1]}")

# Handle missing values in key numerical features
numerical_features = ['mileage', 'year', 'horsepower', 'engine_displacement', 'city_fuel_economy', 'highway_fuel_economy']

for col in numerical_features:
    if col in cars_df.columns:
        missing_count = cars_df[col].isnull().sum()
        if missing_count > 0:
            print(f"Imputing {missing_count} missing values in {col}")
            cars_df[col] = cars_df[col].fillna(cars_df[col].median())

# Handle missing values in categorical features
categorical_features = ['make_name', 'model_name', 'body_type', 'fuel_type', 'transmission', 'exterior_color']

for col in categorical_features:
    if col in cars_df.columns:
        missing_count = cars_df[col].isnull().sum()
        if missing_count > 0:
            print(f"Imputing {missing_count} missing values in {col}")
            cars_df[col] = cars_df[col].fillna('Unknown')

# Create age feature from year
current_year = 2021  # Dataset goes up to 2021
cars_df['age'] = current_year - cars_df['year']
print(f"Age feature created. Range: {cars_df['age'].min()} - {cars_df['age'].max()} years")

# Create mileage per year feature
cars_df['mileage_per_year'] = cars_df['mileage'] / (cars_df['age'] + 1)  # +1 to avoid division by zero
print(f"Mileage per year feature created. Mean: {cars_df['mileage_per_year'].mean():.0f} miles/year")

# Create power-to-weight ratio proxy (using engine displacement as weight proxy)
cars_df['power_to_displacement'] = cars_df['horsepower'] / (cars_df['engine_displacement'] + 1)
print(f"Power-to-displacement ratio created. Mean: {cars_df['power_to_displacement'].mean():.2f}")

# Create fuel efficiency feature
cars_df['avg_fuel_economy'] = (cars_df['city_fuel_economy'] + cars_df['highway_fuel_economy']) / 2
print(f"Average fuel economy created. Mean: {cars_df['avg_fuel_economy'].mean():.1f} MPG")

# Select features for modeling
feature_columns = [
    # Numerical features
    'mileage', 'age', 'horsepower', 'engine_displacement', 
    'city_fuel_economy', 'highway_fuel_economy', 'avg_fuel_economy',
    'mileage_per_year', 'power_to_displacement', 'daysonmarket',
    'latitude', 'longitude', 'seller_rating',
    
    # Categorical features
    'make_name', 'model_name', 'body_type', 'fuel_type', 'transmission',
    'exterior_color', 'franchise_dealer'
]

# Keep only available columns
available_features = [col for col in feature_columns if col in cars_df.columns]
print(f"Selected {len(available_features)} features for modeling:")
print(available_features)

# Prepare data for modeling
X = cars_df[available_features].copy()
y = cars_df['price'].copy()

print(f"Feature matrix shape: {X.shape}")
print(f"Target vector shape: {y.shape}")
print(f"Target statistics - Mean: ${y.mean():.2f}, Std: ${y.std():.2f}")

# Split data into train and test sets
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=None
)

print(f"Training set size: {X_train.shape[0]}")
print(f"Test set size: {X_test.shape[0]}")
print(f"Training target mean: ${y_train.mean():.2f}")
print(f"Test target mean: ${y_test.mean():.2f}")

# Encode categorical variables for LightGBM
from sklearn.preprocessing import LabelEncoder

# Identify categorical columns
categorical_cols = X_train.select_dtypes(include=['object', 'category', 'bool']).columns.tolist()
numerical_cols = X_train.select_dtypes(include=[np.number]).columns.tolist()

print(f"Categorical columns: {categorical_cols}")
print(f"Numerical columns: {numerical_cols}")

# Create copies for encoding
X_train_encoded = X_train.copy()
X_test_encoded = X_test.copy()

# Label encode categorical variables
label_encoders = {}
for col in categorical_cols:
    le = LabelEncoder()
    X_train_encoded[col] = le.fit_transform(X_train_encoded[col].astype(str))
    X_test_encoded[col] = le.transform(X_test_encoded[col].astype(str))
    label_encoders[col] = le

print("Categorical encoding completed.")

# Train LightGBM model
lgb_params = {
    'objective': 'regression',
    'metric': 'rmse',
    'boosting_type': 'gbdt',
    'num_leaves': 100,
    'learning_rate': 0.1,
    'feature_fraction': 0.8,
    'bagging_fraction': 0.8,
    'bagging_freq': 5,
    'verbose': -1,
    'random_state': 42
}

# Create LightGBM datasets
train_data = lgb.Dataset(X_train_encoded, label=y_train)
valid_data = lgb.Dataset(X_test_encoded, label=y_test, reference=train_data)

# Train model
lgb_model = lgb.train(
    lgb_params,
    train_data,
    valid_sets=[train_data, valid_data],
    num_boost_round=1000,
    callbacks=[lgb.early_stopping(50), lgb.log_evaluation(100)]
)

print("LightGBM training completed.")

# Make predictions with LightGBM
y_pred_lgb_train = lgb_model.predict(X_train_encoded)
y_pred_lgb_test = lgb_model.predict(X_test_encoded)

# Calculate metrics
lgb_train_r2 = r2_score(y_train, y_pred_lgb_train)
lgb_test_r2 = r2_score(y_test, y_pred_lgb_test)
lgb_train_mae = mean_absolute_error(y_train, y_pred_lgb_train)
lgb_test_mae = mean_absolute_error(y_test, y_pred_lgb_test)
lgb_train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_lgb_train))
lgb_test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_lgb_test))

print("\n=== LightGBM Results ===")
print(f"Training R²: {lgb_train_r2:.4f}")
print(f"Test R²: {lgb_test_r2:.4f}")
print(f"Training MAE: ${lgb_train_mae:.2f}")
print(f"Test MAE: ${lgb_test_mae:.2f}")
print(f"Training RMSE: ${lgb_train_rmse:.2f}")
print(f"Test RMSE: ${lgb_test_rmse:.2f}")

# Prepare data for CatBoost (can handle categorical features natively)
cat_features = [X_train.columns.get_loc(col) for col in categorical_cols]
print(f"Categorical feature indices for CatBoost: {cat_features}")

# Train CatBoost model
catboost_model = CatBoostRegressor(
    iterations=1000,
    learning_rate=0.1,
    depth=8,
    l2_leaf_reg=3,
    cat_features=cat_features,
    random_seed=42,
    verbose=100,
    early_stopping_rounds=50
)

# Fit the model
catboost_model.fit(
    X_train, y_train,
    eval_set=(X_test, y_test),
    use_best_model=True
)

print("CatBoost training completed.")

# Make predictions with CatBoost
y_pred_cat_train = catboost_model.predict(X_train)
y_pred_cat_test = catboost_model.predict(X_test)

# Calculate metrics
cat_train_r2 = r2_score(y_train, y_pred_cat_train)
cat_test_r2 = r2_score(y_test, y_pred_cat_test)
cat_train_mae = mean_absolute_error(y_train, y_pred_cat_train)
cat_test_mae = mean_absolute_error(y_test, y_pred_cat_test)
cat_train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_cat_train))
cat_test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_cat_test))

print("\n=== CatBoost Results ===")
print(f"Training R²: {cat_train_r2:.4f}")
print(f"Test R²: {cat_test_r2:.4f}")
print(f"Training MAE: ${cat_train_mae:.2f}")
print(f"Test MAE: ${cat_test_mae:.2f}")
print(f"Training RMSE: ${cat_train_rmse:.2f}")
print(f"Test RMSE: ${cat_test_rmse:.2f}")

# Choose the better performing base model
if lgb_test_r2 > cat_test_r2:
    base_model = lgb_model
    base_predictions_train = y_pred_lgb_train
    base_predictions_test = y_pred_lgb_test
    base_model_name = "LightGBM"
    base_r2 = lgb_test_r2
else:
    base_model = catboost_model
    base_predictions_train = y_pred_cat_train
    base_predictions_test = y_pred_cat_test
    base_model_name = "CatBoost"
    base_r2 = cat_test_r2

print(f"Selected {base_model_name} as base model (R² = {base_r2:.4f})")

# Calculate residuals (errors) from the base model
train_residuals = y_train - base_predictions_train
test_residuals = y_test - base_predictions_test

print(f"Training residuals - Mean: ${train_residuals.mean():.2f}, Std: ${train_residuals.std():.2f}")
print(f"Test residuals - Mean: ${test_residuals.mean():.2f}, Std: ${test_residuals.std():.2f}")

# Train error model to predict residuals
error_model = RandomForestRegressor(
    n_estimators=200,
    max_depth=10,
    min_samples_split=10,
    min_samples_leaf=5,
    random_state=42,
    n_jobs=-1
)

# Use encoded features for error model
error_model.fit(X_train_encoded, train_residuals)

print("Error model training completed.")

# Make predictions with error model
predicted_errors_train = error_model.predict(X_train_encoded)
predicted_errors_test = error_model.predict(X_test_encoded)

# Combine base model predictions with error corrections
final_predictions_train = base_predictions_train + predicted_errors_train
final_predictions_test = base_predictions_test + predicted_errors_test

# Calculate final metrics
final_train_r2 = r2_score(y_train, final_predictions_train)
final_test_r2 = r2_score(y_test, final_predictions_test)
final_train_mae = mean_absolute_error(y_train, final_predictions_train)
final_test_mae = mean_absolute_error(y_test, final_predictions_test)
final_train_rmse = np.sqrt(mean_squared_error(y_train, final_predictions_train))
final_test_rmse = np.sqrt(mean_squared_error(y_test, final_predictions_test))

print("\n=== Error-Model + Base Model Results ===")
print(f"Training R²: {final_train_r2:.4f}")
print(f"Test R²: {final_test_r2:.4f}")
print(f"Training MAE: ${final_train_mae:.2f}")
print(f"Test MAE: ${final_test_mae:.2f}")
print(f"Training RMSE: ${final_train_rmse:.2f}")
print(f"Test RMSE: ${final_test_rmse:.2f}")

# Create comparison table
results_df = pd.DataFrame({
    'Model': ['LightGBM', 'CatBoost', 'Error-Model + Base'],
    'Train_R2': [lgb_train_r2, cat_train_r2, final_train_r2],
    'Test_R2': [lgb_test_r2, cat_test_r2, final_test_r2],
    'Test_MAE': [lgb_test_mae, cat_test_mae, final_test_mae],
    'Test_RMSE': [lgb_test_rmse, cat_test_rmse, final_test_rmse]
})

print("\n=== MODEL COMPARISON ===")
print(results_df.round(4))

# Find best model
best_model_idx = results_df['Test_R2'].idxmax()
best_model_name = results_df.loc[best_model_idx, 'Model']
best_r2 = results_df.loc[best_model_idx, 'Test_R2']

print(f"\nBest performing model: {best_model_name}")
print(f"Best R² score: {best_r2:.4f}")
print(f"Target achieved (R² ≥ 0.85): {'✓ YES' if best_r2 >= 0.85 else '✗ NO'}")

# Visualize model performance
plt.figure(figsize=(15, 10))

# R² comparison
plt.subplot(2, 3, 1)
models = results_df['Model']
r2_scores = results_df['Test_R2']
bars = plt.bar(models, r2_scores, color=['skyblue', 'lightgreen', 'coral'])
plt.axhline(y=0.85, color='red', linestyle='--', label='Target R² = 0.85')
plt.title('Model R² Comparison')
plt.ylabel('R² Score')
plt.legend()
plt.xticks(rotation=45)

# Add value labels on bars
for bar, score in zip(bars, r2_scores):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
             f'{score:.3f}', ha='center', va='bottom')

# Prediction vs Actual scatter plot for best model
plt.subplot(2, 3, 2)
if best_model_name == 'LightGBM':
    y_pred_best = y_pred_lgb_test
elif best_model_name == 'CatBoost':
    y_pred_best = y_pred_cat_test
else:
    y_pred_best = final_predictions_test

plt.scatter(y_test, y_pred_best, alpha=0.5, s=1)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
plt.xlabel('Actual Price ($)')
plt.ylabel('Predicted Price ($)')
plt.title(f'{best_model_name} - Predictions vs Actual')

# Residuals plot
plt.subplot(2, 3, 3)
residuals = y_test - y_pred_best
plt.scatter(y_pred_best, residuals, alpha=0.5, s=1)
plt.axhline(y=0, color='red', linestyle='--')
plt.xlabel('Predicted Price ($)')
plt.ylabel('Residuals ($)')
plt.title(f'{best_model_name} - Residuals Plot')

# Feature importance (if available)
plt.subplot(2, 3, 4)
if best_model_name == 'LightGBM':
    importance = lgb_model.feature_importance(importance_type='gain')
    feature_names = X_train_encoded.columns
elif best_model_name == 'CatBoost':
    importance = catboost_model.feature_importances_
    feature_names = X_train.columns
else:
    importance = error_model.feature_importances_
    feature_names = X_train_encoded.columns

# Get top 10 features
top_indices = np.argsort(importance)[-10:]
plt.barh(range(len(top_indices)), importance[top_indices])
plt.yticks(range(len(top_indices)), [feature_names[i] for i in top_indices])
plt.xlabel('Feature Importance')
plt.title(f'{best_model_name} - Top 10 Features')

# Error distribution
plt.subplot(2, 3, 5)
plt.hist(residuals, bins=50, alpha=0.7, color='lightblue')
plt.xlabel('Residuals ($)')
plt.ylabel('Frequency')
plt.title('Residuals Distribution')

# MAE comparison
plt.subplot(2, 3, 6)
mae_scores = results_df['Test_MAE']
bars = plt.bar(models, mae_scores, color=['skyblue', 'lightgreen', 'coral'])
plt.title('Model MAE Comparison')
plt.ylabel('Mean Absolute Error ($)')
plt.xticks(rotation=45)

# Add value labels on bars
for bar, score in zip(bars, mae_scores):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 100, 
             f'${score:.0f}', ha='center', va='bottom')

plt.tight_layout()
plt.show()

# Check if optimization is needed
if best_r2 < 0.85:
    print(f"Current best R² ({best_r2:.4f}) is below target (0.85). Starting optimization...")
    
    # Hyperparameter tuning for LightGBM
    from sklearn.model_selection import RandomizedSearchCV
    
    lgb_param_grid = {
        'num_leaves': [50, 100, 150, 200],
        'learning_rate': [0.05, 0.1, 0.15],
        'feature_fraction': [0.7, 0.8, 0.9],
        'bagging_fraction': [0.7, 0.8, 0.9],
        'min_child_samples': [10, 20, 30]
    }
    
    # Create LightGBM regressor for sklearn
    lgb_regressor = lgb.LGBMRegressor(
        objective='regression',
        boosting_type='gbdt',
        random_state=42,
        n_estimators=500
    )
    
    # Randomized search
    lgb_random_search = RandomizedSearchCV(
        lgb_regressor,
        lgb_param_grid,
        n_iter=20,
        cv=3,
        scoring='r2',
        random_state=42,
        n_jobs=-1
    )
    
    # Fit the search
    lgb_random_search.fit(X_train_encoded, y_train)
    
    # Get best model
    best_lgb = lgb_random_search.best_estimator_
    
    # Make predictions
    y_pred_optimized = best_lgb.predict(X_test_encoded)
    optimized_r2 = r2_score(y_test, y_pred_optimized)
    
    print(f"Optimized LightGBM R²: {optimized_r2:.4f}")
    print(f"Best parameters: {lgb_random_search.best_params_}")
    
    # Update best model if improved
    if optimized_r2 > best_r2:
        print(f"Optimization successful! R² improved from {best_r2:.4f} to {optimized_r2:.4f}")
        best_r2 = optimized_r2
        best_model_name = "Optimized LightGBM"
    
else:
    print(f"Target R² achieved ({best_r2:.4f} ≥ 0.85). No optimization needed.")

# Final summary
print("\n" + "="*60)
print("USED CARS PRICE PREDICTION - FINAL RESULTS")
print("="*60)
print(f"Dataset Size: {len(cars_df):,} records")
print(f"Features Used: {len(available_features)}")
print(f"Training Set: {len(X_train):,} records")
print(f"Test Set: {len(X_test):,} records")
print("\nMODEL PERFORMANCE:")
print("-" * 30)
for idx, row in results_df.iterrows():
    print(f"{row['Model']:<20} R²: {row['Test_R2']:.4f}  MAE: ${row['Test_MAE']:.0f}")

print(f"\nBEST MODEL: {best_model_name}")
print(f"BEST R² SCORE: {best_r2:.4f}")
print(f"TARGET ACHIEVED: {'✓ YES' if best_r2 >= 0.85 else '✗ NO'}")

if best_r2 >= 0.85:
    print("\n🎉 SUCCESS: Target R² ≥ 0.85 achieved!")
    print("The model can accurately predict used car prices.")
else:
    print("\n⚠️  Target not yet achieved. Consider:")
    print("- Additional feature engineering")
    print("- More advanced ensemble methods")
    print("- Deep learning approaches")
    print("- More extensive hyperparameter tuning")

print("\n" + "="*60)