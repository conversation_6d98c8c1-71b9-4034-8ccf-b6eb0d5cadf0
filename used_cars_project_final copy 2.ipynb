{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# US Used Cars (2006 - 2020)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This dataset contains detailed information on approximately 3 million used car listings across the United States from 2005–2020. It is provided as a well-structured CSV (about 9 GB) with dozens of features per car"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The dataset provides ~66 features per car, encompassing numeric attributes (mileage, engine size, year, horsepower, etc.) and categorical descriptors (make, model, body type, fuel type, etc.). This richness enables creative feature engineering and the exploration of multiple feature types (numeric, categorical,\n", "ordinal)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Preprocessing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The data is already in a cleaned, structured format (compiled via a web crawler from CarGurus listings). Most fields are well-defined and ready to use. Only a few columns have missing values (e.g. ~9 features with >50% missing can be dropped upfront). And standard techniques like imputation can address any\n", "remaining gaps. No extensive data scraping or text parsing is needed to get started."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Loading"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import lightgbm as lgb\n", "import ast\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error\n", "from catboost import CatBoostRegressor, Pool\n", "from sklearn.impute import SimpleImputer\n", "from category_encoders import TargetEncoder"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import kagglehub"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Path to dataset files: C:\\Users\\<USER>\\.cache\\kagglehub\\datasets\\ananaymital\\us-used-cars-dataset\\versions\\1\n"]}], "source": ["path = kagglehub.dataset_download(\"ananaymital/us-used-cars-dataset\")\n", "print(\"Path to dataset files:\", path)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["df_path = r\"C:\\Users\\<USER>\\.cache\\kagglehub\\datasets\\ananaymital\\us-used-cars-dataset\\versions\\1\\used_cars_data.csv\""]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["data_types = {\n", "    'vin': 'object',\n", "    'back_legroom': 'object',\n", "    'bed': 'object',\n", "    'bed_height': 'object',\n", "    'bed_length': 'object',\n", "    'body_type': 'category',\n", "    'cabin': 'object',\n", "    'city': 'category',\n", "    'city_fuel_economy': 'float32',\n", "    'combine_fuel_economy': 'float32',\n", "    'daysonmarket': 'int16',\n", "    'dealer_zip': 'object',\n", "    'description': 'object',\n", "    'engine_cylinders': 'category',\n", "    'engine_displacement': 'float32',\n", "    'engine_type': 'object',\n", "    'exterior_color': 'category',\n", "    'fleet': 'object',\n", "    'frame_damaged': 'object',\n", "    'franchise_dealer': 'bool',\n", "    'franchise_make': 'object',\n", "    'front_legroom': 'object',\n", "    'fuel_tank_volume': 'object',\n", "    'fuel_type': 'object',\n", "    'has_accidents': 'object',\n", "    'height': 'object',\n", "    'highway_fuel_economy': 'float32',\n", "    'horsepower': 'float32',\n", "    'interior_color': 'object',\n", "    'isCab': 'object',\n", "    'is_certified': 'float32',\n", "    'is_cpo': 'object',\n", "    'is_new': 'bool',\n", "    'is_oemcpo': 'object',\n", "    'latitude': 'float32',\n", "    'length': 'object',\n", "    'listed_date': 'object',\n", "    'listing_color': 'object',\n", "    'listing_id': 'int64',\n", "    'longitude': 'float32',\n", "    'main_picture_url': 'object',\n", "    'major_options': 'object',\n", "    'make_name': 'object',\n", "    'maximum_seating': 'object',\n", "    'mileage': 'float32',\n", "    'model_name': 'object',\n", "    'owner_count': 'float32',\n", "    'power': 'object',\n", "    'price': 'float32',\n", "    'salvage': 'object',\n", "    'savings_amount': 'int64',\n", "    'seller_rating': 'float32',\n", "    'sp_id': 'float32',\n", "    'sp_name': 'object',\n", "    'theft_title': 'object',\n", "    'torque': 'object',\n", "    'transmission': 'object',\n", "    'transmission_display': 'object',\n", "    'trimId': 'object',\n", "    'trim_name': 'object',\n", "    'vehicle_damage_category': 'float32',\n", "    'wheel_system': 'object',\n", "    'wheel_system_display': 'object',\n", "    'wheelbase': 'object',\n", "    'width': 'object',\n", "    'year': 'int32'\n", "}"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["cars_df = pd.read_csv(df_path, dtype=data_types)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3000040 entries, 0 to 3000039\n", "Data columns (total 66 columns):\n", " #   Column                   Dtype   \n", "---  ------                   -----   \n", " 0   vin                      object  \n", " 1   back_legroom             object  \n", " 2   bed                      object  \n", " 3   bed_height               object  \n", " 4   bed_length               object  \n", " 5   body_type                category\n", " 6   cabin                    object  \n", " 7   city                     category\n", " 8   city_fuel_economy        float32 \n", " 9   combine_fuel_economy     float32 \n", " 10  daysonmarket             int16   \n", " 11  dealer_zip               object  \n", " 12  description              object  \n", " 13  engine_cylinders         category\n", " 14  engine_displacement      float32 \n", " 15  engine_type              object  \n", " 16  exterior_color           category\n", " 17  fleet                    object  \n", " 18  frame_damaged            object  \n", " 19  franchise_dealer         bool    \n", " 20  franchise_make           object  \n", " 21  front_legroom            object  \n", " 22  fuel_tank_volume         object  \n", " 23  fuel_type                object  \n", " 24  has_accidents            object  \n", " 25  height                   object  \n", " 26  highway_fuel_economy     float32 \n", " 27  horsepower               float32 \n", " 28  interior_color           object  \n", " 29  isCab                    object  \n", " 30  is_certified             float32 \n", " 31  is_cpo                   object  \n", " 32  is_new                   bool    \n", " 33  is_oemcpo                object  \n", " 34  latitude                 float32 \n", " 35  length                   object  \n", " 36  listed_date              object  \n", " 37  listing_color            object  \n", " 38  listing_id               int64   \n", " 39  longitude                float32 \n", " 40  main_picture_url         object  \n", " 41  major_options            object  \n", " 42  make_name                object  \n", " 43  maximum_seating          object  \n", " 44  mileage                  float32 \n", " 45  model_name               object  \n", " 46  owner_count              float32 \n", " 47  power                    object  \n", " 48  price                    float32 \n", " 49  salvage                  object  \n", " 50  savings_amount           int64   \n", " 51  seller_rating            float32 \n", " 52  sp_id                    float32 \n", " 53  sp_name                  object  \n", " 54  theft_title              object  \n", " 55  torque                   object  \n", " 56  transmission             object  \n", " 57  transmission_display     object  \n", " 58  trimId                   object  \n", " 59  trim_name                object  \n", " 60  vehicle_damage_category  float32 \n", " 61  wheel_system             object  \n", " 62  wheel_system_display     object  \n", " 63  wheelbase                object  \n", " 64  width                    object  \n", " 65  year                     int32   \n", "dtypes: bool(2), category(4), float32(14), int16(1), int32(1), int64(2), object(42)\n", "memory usage: 1.2+ GB\n"]}], "source": ["cars_df.info()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "city_fuel_economy", "rawType": "float64", "type": "float"}, {"name": "combine_fuel_economy", "rawType": "float64", "type": "float"}, {"name": "daysonmarket", "rawType": "float64", "type": "float"}, {"name": "engine_displacement", "rawType": "float64", "type": "float"}, {"name": "highway_fuel_economy", "rawType": "float64", "type": "float"}, {"name": "horsepower", "rawType": "float64", "type": "float"}, {"name": "is_certified", "rawType": "float64", "type": "float"}, {"name": "latitude", "rawType": "float64", "type": "float"}, {"name": "listing_id", "rawType": "float64", "type": "float"}, {"name": "longitude", "rawType": "float64", "type": "float"}, {"name": "mileage", "rawType": "float64", "type": "float"}, {"name": "owner_count", "rawType": "float64", "type": "float"}, {"name": "price", "rawType": "float64", "type": "float"}, {"name": "savings_amount", "rawType": "float64", "type": "float"}, {"name": "seller_rating", "rawType": "float64", "type": "float"}, {"name": "sp_id", "rawType": "float64", "type": "float"}, {"name": "vehicle_damage_category", "rawType": "float64", "type": "float"}, {"name": "year", "rawType": "float64", "type": "float"}], "ref": "4e986ab9-d54b-4650-bef0-b8fdbd38eed8", "rows": [["count", "2508755.0", "0.0", "3000040.0", "2827654.0", "2508755.0", "2827654.0", "0.0", "3000040.0", "3000040.0", "3000040.0", "2855653.0", "1483027.0", "3000040.0", "3000040.0", "2959168.0", "2999944.0", "0.0", "3000040.0"], ["mean", "22.69325828552246", null, "76.05972920361062", "2968.486328125", "29.473373413085938", "247.99574279785156", null, "36.98415756225586", "275498673.82553065", "-90.6422348022461", "31146.888671875", "1.5326443910598755", "29933.369140625", "550.9768136424848", "4.270412445068359", "233522.171875", null, "2017.728316622445"], ["std", "8.807024002075195", null, "108.8838514520447", "1348.904541015625", "7.769252300262451", "90.46638488769531", null, "4.996819019317627", "8894122.67589518", "13.905889511108398", "74586.7421875", "0.9202927947044373", "19566.169921875", "1079.4477007536384", "0.5133017301559448", "132322.0625", null, "4.1787014519792125"], ["min", "7.0", null, "0.0", "700.0", "10.0", "55.0", null, "18.34670066833496", "19946203.0", "-157.92799377441406", "0.0", "1.0", "165.0", "0.0", "1.0", "41593.0", null, "1915.0"], ["25%", "18.0", null, "14.0", "2000.0", "25.0", "175.0", null, "33.50920104980469", "274579410.5", "-97.08820343017578", "6.0", "1.0", "18451.0", "0.0", "4.0", "63375.0", null, "2017.0"], ["50%", "21.0", null, "35.0", "2500.0", "29.0", "244.0", null, "37.84709930419922", "278545307.0", "-87.24949645996094", "8267.0", "1.0", "26477.0", "0.0", "4.341463565826416", "281627.0", null, "2020.0"], ["75%", "26.0", null, "82.0", "3600.0", "33.0", "300.0", null, "41.00619888305664", "280455336.25", "-80.45490264892578", "43662.0", "2.0", "38220.0", "785.0", "4.6052632331848145", "336614.0", null, "2020.0"], ["max", "127.0", null, "3599.0", "8400.0", "127.0", "1001.0", null, "61.20309829711914", "282022171.0", "-66.07849884033203", "99999984.0", "19.0", "3299995.0", "147414.0", "5.0", "440951.0", null, "2021.0"]], "shape": {"columns": 18, "rows": 8}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>city_fuel_economy</th>\n", "      <th>combine_fuel_economy</th>\n", "      <th>daysonmarket</th>\n", "      <th>engine_displacement</th>\n", "      <th>highway_fuel_economy</th>\n", "      <th>horsepower</th>\n", "      <th>is_certified</th>\n", "      <th>latitude</th>\n", "      <th>listing_id</th>\n", "      <th>longitude</th>\n", "      <th>mileage</th>\n", "      <th>owner_count</th>\n", "      <th>price</th>\n", "      <th>savings_amount</th>\n", "      <th>seller_rating</th>\n", "      <th>sp_id</th>\n", "      <th>vehicle_damage_category</th>\n", "      <th>year</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>2.508755e+06</td>\n", "      <td>0.0</td>\n", "      <td>3.000040e+06</td>\n", "      <td>2.827654e+06</td>\n", "      <td>2.508755e+06</td>\n", "      <td>2.827654e+06</td>\n", "      <td>0.0</td>\n", "      <td>3.000040e+06</td>\n", "      <td>3.000040e+06</td>\n", "      <td>3.000040e+06</td>\n", "      <td>2.855653e+06</td>\n", "      <td>1.483027e+06</td>\n", "      <td>3.000040e+06</td>\n", "      <td>3.000040e+06</td>\n", "      <td>2.959168e+06</td>\n", "      <td>2.999944e+06</td>\n", "      <td>0.0</td>\n", "      <td>3.000040e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2.269326e+01</td>\n", "      <td>NaN</td>\n", "      <td>7.605973e+01</td>\n", "      <td>2.968486e+03</td>\n", "      <td>2.947337e+01</td>\n", "      <td>2.479957e+02</td>\n", "      <td>NaN</td>\n", "      <td>3.698416e+01</td>\n", "      <td>2.754987e+08</td>\n", "      <td>-9.064223e+01</td>\n", "      <td>3.114689e+04</td>\n", "      <td>1.532644e+00</td>\n", "      <td>2.993337e+04</td>\n", "      <td>5.509768e+02</td>\n", "      <td>4.270412e+00</td>\n", "      <td>2.335222e+05</td>\n", "      <td>NaN</td>\n", "      <td>2.017728e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>8.807024e+00</td>\n", "      <td>NaN</td>\n", "      <td>1.088839e+02</td>\n", "      <td>1.348905e+03</td>\n", "      <td>7.769252e+00</td>\n", "      <td>9.046638e+01</td>\n", "      <td>NaN</td>\n", "      <td>4.996819e+00</td>\n", "      <td>8.894123e+06</td>\n", "      <td>1.390589e+01</td>\n", "      <td>7.458674e+04</td>\n", "      <td>9.202928e-01</td>\n", "      <td>1.956617e+04</td>\n", "      <td>1.079448e+03</td>\n", "      <td>5.133017e-01</td>\n", "      <td>1.323221e+05</td>\n", "      <td>NaN</td>\n", "      <td>4.178701e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>7.000000e+00</td>\n", "      <td>NaN</td>\n", "      <td>0.000000e+00</td>\n", "      <td>7.000000e+02</td>\n", "      <td>1.000000e+01</td>\n", "      <td>5.500000e+01</td>\n", "      <td>NaN</td>\n", "      <td>1.834670e+01</td>\n", "      <td>1.994620e+07</td>\n", "      <td>-1.579280e+02</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.650000e+02</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>4.159300e+04</td>\n", "      <td>NaN</td>\n", "      <td>1.915000e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.800000e+01</td>\n", "      <td>NaN</td>\n", "      <td>1.400000e+01</td>\n", "      <td>2.000000e+03</td>\n", "      <td>2.500000e+01</td>\n", "      <td>1.750000e+02</td>\n", "      <td>NaN</td>\n", "      <td>3.350920e+01</td>\n", "      <td>2.745794e+08</td>\n", "      <td>-9.708820e+01</td>\n", "      <td>6.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.845100e+04</td>\n", "      <td>0.000000e+00</td>\n", "      <td>4.000000e+00</td>\n", "      <td>6.337500e+04</td>\n", "      <td>NaN</td>\n", "      <td>2.017000e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>2.100000e+01</td>\n", "      <td>NaN</td>\n", "      <td>3.500000e+01</td>\n", "      <td>2.500000e+03</td>\n", "      <td>2.900000e+01</td>\n", "      <td>2.440000e+02</td>\n", "      <td>NaN</td>\n", "      <td>3.784710e+01</td>\n", "      <td>2.785453e+08</td>\n", "      <td>-8.724950e+01</td>\n", "      <td>8.267000e+03</td>\n", "      <td>1.000000e+00</td>\n", "      <td>2.647700e+04</td>\n", "      <td>0.000000e+00</td>\n", "      <td>4.341464e+00</td>\n", "      <td>2.816270e+05</td>\n", "      <td>NaN</td>\n", "      <td>2.020000e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2.600000e+01</td>\n", "      <td>NaN</td>\n", "      <td>8.200000e+01</td>\n", "      <td>3.600000e+03</td>\n", "      <td>3.300000e+01</td>\n", "      <td>3.000000e+02</td>\n", "      <td>NaN</td>\n", "      <td>4.100620e+01</td>\n", "      <td>2.804553e+08</td>\n", "      <td>-8.045490e+01</td>\n", "      <td>4.366200e+04</td>\n", "      <td>2.000000e+00</td>\n", "      <td>3.822000e+04</td>\n", "      <td>7.850000e+02</td>\n", "      <td>4.605263e+00</td>\n", "      <td>3.366140e+05</td>\n", "      <td>NaN</td>\n", "      <td>2.020000e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.270000e+02</td>\n", "      <td>NaN</td>\n", "      <td>3.599000e+03</td>\n", "      <td>8.400000e+03</td>\n", "      <td>1.270000e+02</td>\n", "      <td>1.001000e+03</td>\n", "      <td>NaN</td>\n", "      <td>6.120310e+01</td>\n", "      <td>2.820222e+08</td>\n", "      <td>-6.607850e+01</td>\n", "      <td>9.999998e+07</td>\n", "      <td>1.900000e+01</td>\n", "      <td>3.299995e+06</td>\n", "      <td>1.474140e+05</td>\n", "      <td>5.000000e+00</td>\n", "      <td>4.409510e+05</td>\n", "      <td>NaN</td>\n", "      <td>2.021000e+03</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       city_fuel_economy  combine_fuel_economy  daysonmarket  \\\n", "count       2.508755e+06                   0.0  3.000040e+06   \n", "mean        2.269326e+01                   NaN  7.605973e+01   \n", "std         8.807024e+00                   NaN  1.088839e+02   \n", "min         7.000000e+00                   NaN  0.000000e+00   \n", "25%         1.800000e+01                   NaN  1.400000e+01   \n", "50%         2.100000e+01                   NaN  3.500000e+01   \n", "75%         2.600000e+01                   NaN  8.200000e+01   \n", "max         1.270000e+02                   NaN  3.599000e+03   \n", "\n", "       engine_displacement  highway_fuel_economy    horsepower  is_certified  \\\n", "count         2.827654e+06          2.508755e+06  2.827654e+06           0.0   \n", "mean          2.968486e+03          2.947337e+01  2.479957e+02           NaN   \n", "std           1.348905e+03          7.769252e+00  9.046638e+01           NaN   \n", "min           7.000000e+02          1.000000e+01  5.500000e+01           NaN   \n", "25%           2.000000e+03          2.500000e+01  1.750000e+02           NaN   \n", "50%           2.500000e+03          2.900000e+01  2.440000e+02           NaN   \n", "75%           3.600000e+03          3.300000e+01  3.000000e+02           NaN   \n", "max           8.400000e+03          1.270000e+02  1.001000e+03           NaN   \n", "\n", "           latitude    listing_id     longitude       mileage   owner_count  \\\n", "count  3.000040e+06  3.000040e+06  3.000040e+06  2.855653e+06  1.483027e+06   \n", "mean   3.698416e+01  2.754987e+08 -9.064223e+01  3.114689e+04  1.532644e+00   \n", "std    4.996819e+00  8.894123e+06  1.390589e+01  7.458674e+04  9.202928e-01   \n", "min    1.834670e+01  1.994620e+07 -1.579280e+02  0.000000e+00  1.000000e+00   \n", "25%    3.350920e+01  2.745794e+08 -9.708820e+01  6.000000e+00  1.000000e+00   \n", "50%    3.784710e+01  2.785453e+08 -8.724950e+01  8.267000e+03  1.000000e+00   \n", "75%    4.100620e+01  2.804553e+08 -8.045490e+01  4.366200e+04  2.000000e+00   \n", "max    6.120310e+01  2.820222e+08 -6.607850e+01  9.999998e+07  1.900000e+01   \n", "\n", "              price  savings_amount  seller_rating         sp_id  \\\n", "count  3.000040e+06    3.000040e+06   2.959168e+06  2.999944e+06   \n", "mean   2.993337e+04    5.509768e+02   4.270412e+00  2.335222e+05   \n", "std    1.956617e+04    1.079448e+03   5.133017e-01  1.323221e+05   \n", "min    1.650000e+02    0.000000e+00   1.000000e+00  4.159300e+04   \n", "25%    1.845100e+04    0.000000e+00   4.000000e+00  6.337500e+04   \n", "50%    2.647700e+04    0.000000e+00   4.341464e+00  2.816270e+05   \n", "75%    3.822000e+04    7.850000e+02   4.605263e+00  3.366140e+05   \n", "max    3.299995e+06    1.474140e+05   5.000000e+00  4.409510e+05   \n", "\n", "       vehicle_damage_category          year  \n", "count                      0.0  3.000040e+06  \n", "mean                       NaN  2.017728e+03  \n", "std                        NaN  4.178701e+00  \n", "min                        NaN  1.915000e+03  \n", "25%                        NaN  2.017000e+03  \n", "50%                        NaN  2.020000e+03  \n", "75%                        NaN  2.020000e+03  \n", "max                        NaN  2.021000e+03  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["cars_df.describe()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "vin", "rawType": "object", "type": "string"}, {"name": "back_legroom", "rawType": "object", "type": "string"}, {"name": "bed", "rawType": "object", "type": "unknown"}, {"name": "bed_height", "rawType": "object", "type": "unknown"}, {"name": "bed_length", "rawType": "object", "type": "unknown"}, {"name": "body_type", "rawType": "category", "type": "unknown"}, {"name": "cabin", "rawType": "object", "type": "unknown"}, {"name": "city", "rawType": "category", "type": "unknown"}, {"name": "city_fuel_economy", "rawType": "float32", "type": "float"}, {"name": "combine_fuel_economy", "rawType": "float32", "type": "float"}, {"name": "daysonmarket", "rawType": "int16", "type": "integer"}, {"name": "dealer_zip", "rawType": "object", "type": "string"}, {"name": "description", "rawType": "object", "type": "unknown"}, {"name": "engine_cylinders", "rawType": "category", "type": "unknown"}, {"name": "engine_displacement", "rawType": "float32", "type": "float"}, {"name": "engine_type", "rawType": "object", "type": "string"}, {"name": "exterior_color", "rawType": "category", "type": "unknown"}, {"name": "fleet", "rawType": "object", "type": "unknown"}, {"name": "frame_damaged", "rawType": "object", "type": "unknown"}, {"name": "franchise_dealer", "rawType": "bool", "type": "boolean"}, {"name": "franchise_make", "rawType": "object", "type": "string"}, {"name": "front_legroom", "rawType": "object", "type": "string"}, {"name": "fuel_tank_volume", "rawType": "object", "type": "string"}, {"name": "fuel_type", "rawType": "object", "type": "string"}, {"name": "has_accidents", "rawType": "object", "type": "unknown"}, {"name": "height", "rawType": "object", "type": "string"}, {"name": "highway_fuel_economy", "rawType": "float32", "type": "float"}, {"name": "horsepower", "rawType": "float32", "type": "float"}, {"name": "interior_color", "rawType": "object", "type": "unknown"}, {"name": "isCab", "rawType": "object", "type": "unknown"}, {"name": "is_certified", "rawType": "float32", "type": "float"}, {"name": "is_cpo", "rawType": "object", "type": "unknown"}, {"name": "is_new", "rawType": "bool", "type": "boolean"}, {"name": "is_oemcpo", "rawType": "object", "type": "unknown"}, {"name": "latitude", "rawType": "float32", "type": "float"}, {"name": "length", "rawType": "object", "type": "string"}, {"name": "listed_date", "rawType": "object", "type": "string"}, {"name": "listing_color", "rawType": "object", "type": "string"}, {"name": "listing_id", "rawType": "int64", "type": "integer"}, {"name": "longitude", "rawType": "float32", "type": "float"}, {"name": "main_picture_url", "rawType": "object", "type": "unknown"}, {"name": "major_options", "rawType": "object", "type": "unknown"}, {"name": "make_name", "rawType": "object", "type": "string"}, {"name": "maximum_seating", "rawType": "object", "type": "string"}, {"name": "mileage", "rawType": "float32", "type": "float"}, {"name": "model_name", "rawType": "object", "type": "string"}, {"name": "owner_count", "rawType": "float32", "type": "float"}, {"name": "power", "rawType": "object", "type": "string"}, {"name": "price", "rawType": "float32", "type": "float"}, {"name": "salvage", "rawType": "object", "type": "unknown"}, {"name": "savings_amount", "rawType": "int64", "type": "integer"}, {"name": "seller_rating", "rawType": "float32", "type": "float"}, {"name": "sp_id", "rawType": "float32", "type": "float"}, {"name": "sp_name", "rawType": "object", "type": "string"}, {"name": "theft_title", "rawType": "object", "type": "unknown"}, {"name": "torque", "rawType": "object", "type": "string"}, {"name": "transmission", "rawType": "object", "type": "string"}, {"name": "transmission_display", "rawType": "object", "type": "string"}, {"name": "trimId", "rawType": "object", "type": "string"}, {"name": "trim_name", "rawType": "object", "type": "string"}, {"name": "vehicle_damage_category", "rawType": "float32", "type": "float"}, {"name": "wheel_system", "rawType": "object", "type": "string"}, {"name": "wheel_system_display", "rawType": "object", "type": "string"}, {"name": "wheelbase", "rawType": "object", "type": "string"}, {"name": "width", "rawType": "object", "type": "string"}, {"name": "year", "rawType": "int32", "type": "integer"}], "ref": "1a9af66a-74d7-411e-9e49-0a008d0ca772", "rows": [["0", "ZACNJABB5KPJ92081", "35.1 in", null, null, null, "SUV / Crossover", null, "Bayamon", null, null, "522", "00960", "[!@@Additional Info@@!]Engine: 2.4L I4 ZERO EVAP M-AIR,Full Size Temporary Use Spare Tire,Manufacturer's Statement of Origin,Quick Order Package 2XB,Tires: 215/60R17 BSW AS Touring,Transmission: 9-Speed 948TE Automatic,Wheels: 17' x 7.0' Aluminum", "I4", "1300.0", "I4", "Solar Yellow", null, null, "True", "Jeep", "41.2 in", "12.7 gal", "Gasoline", null, "66.5 in", null, "177.0", "Black", null, null, null, "True", null, "18.3988", "166.6 in", "2019-04-06", "YELLOW", "*********", "-66.1582", "https://static.cargurus.com/images/forsale/2020/05/18/18/53/2019_jeep_renegade-pic-3296019950109819645-152x114.jpeg", "['Quick Order Package']", "Jeep", "5 seats", "7.0", "Renegade", null, "177 hp @ 5,750 RPM", "23141.0", null, "0", "2.8", "370599.0", "Flagship Chrysler", null, "200 lb-ft @ 1,750 RPM", "A", "9-Speed Automatic Overdrive", "t83804", "Latitude FWD", null, "FWD", "Front-Wheel Drive", "101.2 in", "79.6 in", "2019"], ["1", "SALCJ2FX1LH858117", "38.1 in", null, null, null, "SUV / Crossover", null, "San Juan", null, null, "207", "00922", "[!@@Additional Info@@!]Keyless Entry,Ebony Morzine Headliner,Chrome Wheel Protection Pack,Powered Tailgate,Loadspace Mat,Wheels: 18' Style 5075 Gloss Sparkle Silver,Cargo Carrier,High Speed Emergency Braking,Adaptive Cruise Control w/Stop & Go,Sunshade,12-Way Electric Front Seats,Rubber Mats,Drive Pack,Basic Rear Seat Convenience Pack,Premium Interior Protection,Blind Spot Assist,Cargo Net", "I4", "2000.0", "I4", "Narvik Black", null, null, "True", "Land Rover", "39.1 in", "17.7 gal", "Gasoline", null, "68 in", null, "246.0", "Black (Ebony)", null, null, null, "True", null, "18.4439", "181 in", "2020-02-15", "BLACK", "265946296", "-66.0785", "https://static.cargurus.com/images/forsale/2020/05/15/18/25/2020_land_rover_discovery_sport-pic-3854830367929838997-152x114.jpeg", "['Adaptive Cruise Control']", "Land Rover", "7 seats", "8.0", "Discovery Sport", null, "246 hp @ 5,500 RPM", "46500.0", null, "0", "3.0", "389227.0", "Land Rover San Juan", null, "269 lb-ft @ 1,400 RPM", "A", "9-Speed Automatic Overdrive", "t86759", "S AWD", null, "AWD", "All-Wheel Drive", "107.9 in", "85.6 in", "2020"], ["2", "JF1VA2M67G9829723", "35.4 in", null, null, null, "Sedan", null, "Guaynabo", "17.0", null, "1233", "00969", null, "H4", "2500.0", "H4", null, "False", "False", "True", "FIAT", "43.3 in", "15.9 gal", "Gasoline", "False", "58.1 in", "23.0", "305.0", null, "False", null, null, "False", null, "18.3467", "180.9 in", "2017-04-25", "UNKNOWN", "173473508", "-66.1098", null, "['Alloy Wheels', 'Blue<PERSON>', 'Backup Camera', 'Heated Seats']", "Subaru", "5 seats", null, "WRX STI", "3.0", "305 hp @ 6,000 RPM", "46995.0", "False", "0", null, "370467.0", "FIAT de San Juan", "False", "290 lb-ft @ 4,000 RPM", "M", "6-Speed Manual", "t58994", "Base", null, "AWD", "All-Wheel Drive", "104.3 in", "78.9 in", "2016"], ["3", "SALRR2RV0L2433391", "37.6 in", null, null, null, "SUV / Crossover", null, "San Juan", null, null, "196", "00922", "[!@@Additional Info@@!]Fog Lights,7 Seat Package,Wheels: 21' 9 Spoke,GVWR: 6,900 lbs,Full Length Black Roof Rails,Twin-Speed Transfer Case,Cargo Carrier,Car Care Kit,Rubber Mat Set,Electronic Air Suspension,Prem Interior Protection/Storage Pack,Ebony Headlining,Wheel Protection Pack Chrome Locks,Cargo Mat,Tire Pressure Gauge,Windshield Sunshade,Basic Rear Seat Convenience Pack,Chrome Wheel Locks,Front Center Console Cooler Compartment,Tires: 21',Cabin Air Ionisation", "V6", "3000.0", "V6", "<PERSON><PERSON>", null, null, "True", "Land Rover", "39 in", "23.5 gal", "Gasoline", null, "73 in", null, "340.0", "Gray (Ebony/Ebony/Ebony)", null, null, null, "True", null, "18.4439", "195.1 in", "2020-02-26", "GRAY", "266911050", "-66.0785", "https://static.cargurus.com/images/forsale/2020/05/18/20/55/2020_land_rover_discovery-pic-3571104253551947583-152x114.jpeg", null, "Land Rover", "7 seats", "11.0", "Discovery", null, "340 hp @ 6,500 RPM", "67430.0", null, "0", "3.0", "389227.0", "Land Rover San Juan", null, "332 lb-ft @ 3,500 RPM", "A", "8-Speed Automatic Overdrive", "t86074", "V6 HSE AWD", null, "AWD", "All-Wheel Drive", "115 in", "87.4 in", "2020"], ["4", "SALCJ2FXXLH862327", "38.1 in", null, null, null, "SUV / Crossover", null, "San Juan", null, null, "137", "00922", "[!@@Additional Info@@!]Keyless Entry,Ebony Morzine Headliner,Chrome Wheel Protection Pack,ClearSight Rearview Mirror,Loadspace Mat,Wheels: 18' Style 5075 Gloss Sparkle Silver,Head-Up Display,Cargo Carrier,High Speed Emergency Braking,Adaptive Cruise Control w/Stop & Go,Sunshade,Technology Pack,12-Way Electric Front Seats,Rubber Mats,Drive Pack,Basic Rear Seat Convenience Pack,Fixed Panoramic Roof,Premium Interior Protection,Interactive Driver Display,Blind Spot Assist,Cargo Net,Wireless Device Charging", "I4", "2000.0", "I4", "Narvik Black", null, null, "True", "Land Rover", "39.1 in", "17.7 gal", "Gasoline", null, "68 in", null, "246.0", "Black (Ebony)", null, null, null, "True", null, "18.4439", "181 in", "2020-04-25", "BLACK", "270957414", "-66.0785", "https://static.cargurus.com/images/forsale/2020/07/19/01/27/2020_land_rover_discovery_sport-pic-2859722089486346228-152x114.jpeg", "['Adaptive Cruise Control']", "Land Rover", "7 seats", "7.0", "Discovery Sport", null, "246 hp @ 5,500 RPM", "48880.0", null, "0", "3.0", "389227.0", "Land Rover San Juan", null, "269 lb-ft @ 1,400 RPM", "A", "9-Speed Automatic Overdrive", "t86759", "S AWD", null, "AWD", "All-Wheel Drive", "107.9 in", "85.6 in", "2020"], ["5", "SALYK2EX1LA261711", "37.1 in", null, null, null, "SUV / Crossover", null, "San Juan", null, null, "242", "00922", "[!@@Additional Info@@!]Tires: 21' All-Season,Adaptive Dynamics,R-Dynamic Black Exterior Package,Loadspace Mat,Cargo Carrier,Wheels: 21' 10 Spoke w/<PERSON>loss Black Finish,High Speed Emergency Braking,Adaptive Cruise Control w/Stop & Go,Sunshade,Front Fog Lights,Gesture Tailgate,Narvik Black Grill Mesh,Black Contrast Roof,Rubber Mats,Premium Interior Protection & Storage Pack,Drive Pack,Basic Rear Seat Convenience Pack,Black Roof Rails,Cargo Net", "I4", "2000.0", "I4", "<PERSON><PERSON><PERSON>", "False", "False", "True", "Land Rover", "40.2 in", "16.6 gal", "Gasoline", "False", "66.3 in", null, "247.0", "<PERSON> (Ebony / Ebony)", "False", null, null, "True", null, "18.4439", "188.9 in", "2020-01-11", "UNKNOWN", "262940541", "-66.0785", "https://static.cargurus.com/images/forsale/2020/05/18/20/55/2020_land_rover_range_rover_velar-pic-8429547440869393875-152x114.jpeg", "['Leather Seats', 'Sunroof/Moonroof', 'Navigation System', 'Adaptive Cruise Control', 'Alloy Wheels', 'Bluetooth', 'Backup Camera', 'Remote Start']", "Land Rover", "5 seats", "12.0", "Range Rover Velar", null, "247 hp @ 5,500 RPM", "66903.0", "False", "0", "3.0", "389227.0", "Land Rover San Juan", "False", "269 lb-ft @ 1,200 RPM", "A", "8-Speed Automatic Overdrive", "t85614", "P250 R-Dynamic S AWD", null, "AWD", "All-Wheel Drive", "113.1 in", "84.4 in", "2020"], ["6", "3MZBPABL6KM107908", "35.1 in", null, null, null, "Sedan", null, "Bayamon", null, null, "447", "00960", "[!@@Additional Info@@!]4-Wheel Disc Brakes,A/C,ABS,AM/FM Stereo,Adjustable Steering Wheel,Aluminum Wheels,Automatic Headlights,Auxiliary Audio Input,Back-Up Camera,Bluetooth Connection,Brake Actuated Limited Slip Differential,Brake Assist,Bucket Seats,Child Safety Locks,<PERSON><PERSON><PERSON> Seats,Cruise Control,Daytime Running Lights,Driver Air Bag,Driver Vanity Mirror,Engine Immobilizer,Floor Mats,Front Head Air Bag,Front Side Air Bag,Front Wheel Drive,HD Radio,Intermittent Wipers,Keyless Entry,Keyless Start,Knee Air Bag,MP3 Player,Pass-Through Rear Seat,Passenger Air Bag,Passenger Air Bag Sensor,Passenger Vanity Mirror,Power Door Locks,Power Mirror(s),Power Steering,Power Windows,Rear Bench Seat,Rear Defrost,Rear Head Air Bag,Requires Subscription,Smart Device Integration,Stability Control,Steering Wheel Audio Controls,Telematics,Temporary Spare Tire,Tire Pressure Monitor,Tires - Front All-Season,Tires - Rear All-Season,Traction Control,Trip Computer,Variable Speed Intermittent Wipers", "I4", "2500.0", "I4", "SONIC SILVER", null, null, "True", "Jeep", "42.3 in", "13.2 gal", "Gasoline", null, "56.9 in", null, "186.0", "Black", null, null, null, "True", null, "18.3988", "183.5 in", "2019-06-20", "SILVER", "244110426", "-66.1582", "https://static.cargurus.com/images/forsale/2019/02/20/17/08/2019_mazda_mazda3-pic-307344583231226018-152x114.jpeg", "['Alloy Wheels', 'Bluetooth', 'Backup Camera']", "Mazda", "5 seats", "14.0", "MAZDA3", null, "186 hp @ 6,000 RPM", "23695.0", null, "0", "2.8", "370599.0", "Flagship Chrysler", null, "186 lb-ft @ 4,000 RPM", "A", "6-Speed Automatic Overdrive", "t85256", "Sedan FWD", null, "FWD", "Front-Wheel Drive", "107.3 in", "70.7 in", "2019"], ["7", "SALYK2EX5LA275434", "37.1 in", null, null, null, "SUV / Crossover", null, "San Juan", null, null, "70", "00922", "[!@@Additional Info@@!]Tires: 21' All-Season,Adaptive Dynamics,R-Dynamic Black Exterior Package,Loadspace Mat,Head-Up Display,Basic Interior Protection & Storage Pack,Cargo Carrier,High Speed Emergency Braking,Adaptive Cruise Control w/Stop & Go,Technology Pack,Front Fog Lights,Narvik Black Grill Mesh,Wheels: 21' 5 Split-Spoke w/Diamond Turned Finish,Wheel Protection Pack Chrome Locks,Rubber Mats,Drive Pack,Basic Rear Seat Convenience Pack,Black Roof Rails,Radio: Meridian Surround Sound System,Interactive Driver Display", "I4", "2000.0", "I4", "Fuji White", null, null, "True", "Land Rover", "40.2 in", "16.6 gal", "Gasoline", null, "66.3 in", null, "247.0", "White (Eclipse / Ebony / Ebony)", null, null, null, "True", null, "18.4439", "188.9 in", "2020-07-01", "WHITE", "275458784", "-66.0785", "https://static.cargurus.com/images/forsale/2020/07/18/23/23/2020_land_rover_range_rover_velar-pic-4574376849965700020-152x114.jpeg", "['Adaptive Cruise Control']", "Land Rover", "5 seats", "11.0", "Range Rover Velar", null, "247 hp @ 5,500 RPM", "68520.0", null, "0", "3.0", "389227.0", "Land Rover San Juan", null, "269 lb-ft @ 1,200 RPM", "A", "8-Speed Automatic Overdrive", "t85614", "P250 R-Dynamic S AWD", null, "AWD", "All-Wheel Drive", "113.1 in", "84.4 in", "2020"], ["8", "SALCJ2FX6LH858128", "38.1 in", null, null, null, "SUV / Crossover", null, "San Juan", null, null, "196", "00922", "[!@@Additional Info@@!]Keyless Entry,Chrome Wheel Protection Pack,Headlight Powerwash,Front Fog Lights,Rubber Mats,Blind Spot Assist,Premium Interior Protection,Powered Tailgate,Two-Zone Climate Control w/2nd Row Vents,<PERSON> Sensing,Loadspace Mat,Wheels: 18' Style 5075 Gloss Sparkle Silver,Cargo Carrier,Auto High-Beam Assist,360 Surround Camera,Sunshade,14-Way Heated Electric Memory Front Seats,Radio: Meridian Sound System w/10 Speakers,Smartphone Pack,Connected Navigation Pro,Hot Climate Pack,Basic Rear Seat Convenience Pack,Fixed Panoramic Roof,SiriusXM Satellite & HD Radio,Cabin Air Ionisation,Cargo Net,Air Quality Sensor,Wireless Device Charging", "I4", "2000.0", "I4", "<PERSON><PERSON>", null, null, "True", "Land Rover", "39.1 in", "17.7 gal", "Gasoline", null, "68 in", null, "246.0", "Black (Ebony)", null, null, null, "True", null, "18.4439", "181 in", "2020-02-26", "GRAY", "266911040", "-66.0785", "https://static.cargurus.com/images/forsale/2020/05/18/20/55/2020_land_rover_discovery_sport-pic-3027506044570298028-152x114.jpeg", "['Navigation System', 'Backup Camera', 'Blind Spot Monitoring']", "Land Rover", "7 seats", "8.0", "Discovery Sport", null, "246 hp @ 5,500 RPM", "51245.0", null, "0", "3.0", "389227.0", "Land Rover San Juan", null, "269 lb-ft @ 1,400 RPM", "A", "9-Speed Automatic Overdrive", "t86759", "S AWD", null, "AWD", "All-Wheel Drive", "107.9 in", "85.6 in", "2020"], ["9", "SALZL2GX4LH007593", "33.8 in", null, null, null, "SUV / Crossover", null, "San Juan", null, null, "510", "00922", "[!@@Additional Info@@!]4-Wheel Disc Brakes,A/C,ABS,Adjustable Steering Wheel,All Wheel Drive,Aluminum Wheels,Auto-Dimming Rearview Mirror,Automatic Headlights,Automatic Parking,Auxiliary Audio Input,Back-Up Camera,Bluetooth Connection,Brake Actuated Limited Slip Differential,Brake Assist,Bucket Seats,Cargo Shade,Child Safety Locks,Climate Control,Cross-Traffic Alert,Cruise Control,Daytime Running Lights,Driver Adjustable Lumbar,Driver Air Bag,Driver Illuminated Vanity Mirror,Driver Vanity Mirror,Engine Immobilizer,Floor Mats,Front Head Air Bag,Front Side Air Bag,Hard Disk Drive Media Storage,Headlights-Auto-Leveling,Heated Mirrors,Integrated Turn Signal Mirrors,Intermittent Wipers,Keyless Entry,Keyless Start,Keyless Start,Lane Departure Warning,Lane Departure Warning,Lane Keeping Assist,Leather Seats,Leather Steering Wheel,MP3 Player,MP3 Player,MP3 Player,Mirror Memory,Multi-Zone A/C,Navigation System,Pass-Through Rear Seat,Passenger Adjustable Lumbar,Passenger Air Bag,Passenger Air Bag Sensor,Passenger Illuminated Visor Mirror,Passenger Vanity Mirror,Power Door Locks,Power Door Locks,Power Door Locks,Power Driver Seat,Power Folding Mirrors,Power Liftgate,Power Mirror(s),Power Passenger Seat,Power Steering,Power Windows,Power Windows,Privacy Glass,Rain Sensing Wipers,Rear Bench Seat,Rear Defrost,Rear Head Air Bag,Rear Spoiler,Remote Engine Start,Remote Trunk Release,Requires Subscription,Seat Memory,Security System,Smart Device Integration,Smart Device Integration,Stability Control,Steering Wheel Audio Controls,Telematics,Temporary Spare Tire,Tire Pressure Monitor,Tires - Front Performance,Tires - Rear Performance,Traction Control,Traction Control,Trip Computer,Trip Computer,Turbocharged,Variable Speed Intermittent Wipers,WiFi Hotspot", "I4", "2000.0", "I4", "<PERSON>", "False", "False", "True", "Land Rover", "40 in", "17.7 gal", "Gasoline", "False", "64.9 in", null, "296.0", "Eclipse/Ebony", "False", null, null, "False", null, "18.4439", "172.1 in", "2019-04-18", "WHITE", "238225156", "-66.0785", "https://static.cargurus.com/images/forsale/2020/05/15/20/27/2020_land_rover_range_rover_evoque-pic-5909390658865522284-152x114.jpeg", "['Leather Seats', 'Navigation System', 'Adaptive Cruise Control', 'Alloy Wheels', 'Bluetooth', 'Backup Camera', 'Remote Start', 'Blind Spot Monitoring']", "Land Rover", "5 seats", "254.0", "Range Rover Evoque", null, "296 hp @ 5,500 RPM", "84399.0", "False", "0", "3.0", "389227.0", "Land Rover San Juan", "False", "295 lb-ft @ 1,600 RPM", "A", "9-Speed Automatic Overdrive", "t85531", "P300 R-Dynamic SE AWD", null, "AWD", "All-Wheel Drive", "105.6 in", "82.7 in", "2020"]], "shape": {"columns": 66, "rows": 10}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>vin</th>\n", "      <th>back_legroom</th>\n", "      <th>bed</th>\n", "      <th>bed_height</th>\n", "      <th>bed_length</th>\n", "      <th>body_type</th>\n", "      <th>cabin</th>\n", "      <th>city</th>\n", "      <th>city_fuel_economy</th>\n", "      <th>combine_fuel_economy</th>\n", "      <th>...</th>\n", "      <th>transmission</th>\n", "      <th>transmission_display</th>\n", "      <th>trimId</th>\n", "      <th>trim_name</th>\n", "      <th>vehicle_damage_category</th>\n", "      <th>wheel_system</th>\n", "      <th>wheel_system_display</th>\n", "      <th>wheelbase</th>\n", "      <th>width</th>\n", "      <th>year</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ZACNJABB5KPJ92081</td>\n", "      <td>35.1 in</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SUV / Crossover</td>\n", "      <td>NaN</td>\n", "      <td>Bayamon</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>A</td>\n", "      <td>9-Speed Automatic Overdrive</td>\n", "      <td>t83804</td>\n", "      <td>Latitude FWD</td>\n", "      <td>NaN</td>\n", "      <td>FWD</td>\n", "      <td>Front-Wheel Drive</td>\n", "      <td>101.2 in</td>\n", "      <td>79.6 in</td>\n", "      <td>2019</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SALCJ2FX1LH858117</td>\n", "      <td>38.1 in</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SUV / Crossover</td>\n", "      <td>NaN</td>\n", "      <td>San Juan</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>A</td>\n", "      <td>9-Speed Automatic Overdrive</td>\n", "      <td>t86759</td>\n", "      <td>S AWD</td>\n", "      <td>NaN</td>\n", "      <td>AWD</td>\n", "      <td>All-Wheel Drive</td>\n", "      <td>107.9 in</td>\n", "      <td>85.6 in</td>\n", "      <td>2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>JF1VA2M67G9829723</td>\n", "      <td>35.4 in</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Sedan</td>\n", "      <td>NaN</td>\n", "      <td>Guaynabo</td>\n", "      <td>17.0</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>M</td>\n", "      <td>6-Speed Manual</td>\n", "      <td>t58994</td>\n", "      <td>Base</td>\n", "      <td>NaN</td>\n", "      <td>AWD</td>\n", "      <td>All-Wheel Drive</td>\n", "      <td>104.3 in</td>\n", "      <td>78.9 in</td>\n", "      <td>2016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SALRR2RV0L2433391</td>\n", "      <td>37.6 in</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SUV / Crossover</td>\n", "      <td>NaN</td>\n", "      <td>San Juan</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>A</td>\n", "      <td>8-Speed Automatic Overdrive</td>\n", "      <td>t86074</td>\n", "      <td>V6 HSE AWD</td>\n", "      <td>NaN</td>\n", "      <td>AWD</td>\n", "      <td>All-Wheel Drive</td>\n", "      <td>115 in</td>\n", "      <td>87.4 in</td>\n", "      <td>2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SALCJ2FXXLH862327</td>\n", "      <td>38.1 in</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SUV / Crossover</td>\n", "      <td>NaN</td>\n", "      <td>San Juan</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>A</td>\n", "      <td>9-Speed Automatic Overdrive</td>\n", "      <td>t86759</td>\n", "      <td>S AWD</td>\n", "      <td>NaN</td>\n", "      <td>AWD</td>\n", "      <td>All-Wheel Drive</td>\n", "      <td>107.9 in</td>\n", "      <td>85.6 in</td>\n", "      <td>2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>SALYK2EX1LA261711</td>\n", "      <td>37.1 in</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SUV / Crossover</td>\n", "      <td>NaN</td>\n", "      <td>San Juan</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>A</td>\n", "      <td>8-Speed Automatic Overdrive</td>\n", "      <td>t85614</td>\n", "      <td>P250 R-Dynamic S AWD</td>\n", "      <td>NaN</td>\n", "      <td>AWD</td>\n", "      <td>All-Wheel Drive</td>\n", "      <td>113.1 in</td>\n", "      <td>84.4 in</td>\n", "      <td>2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3MZBPABL6KM107908</td>\n", "      <td>35.1 in</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Sedan</td>\n", "      <td>NaN</td>\n", "      <td>Bayamon</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>A</td>\n", "      <td>6-Speed Automatic Overdrive</td>\n", "      <td>t85256</td>\n", "      <td>Sedan FWD</td>\n", "      <td>NaN</td>\n", "      <td>FWD</td>\n", "      <td>Front-Wheel Drive</td>\n", "      <td>107.3 in</td>\n", "      <td>70.7 in</td>\n", "      <td>2019</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>SALYK2EX5LA275434</td>\n", "      <td>37.1 in</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SUV / Crossover</td>\n", "      <td>NaN</td>\n", "      <td>San Juan</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>A</td>\n", "      <td>8-Speed Automatic Overdrive</td>\n", "      <td>t85614</td>\n", "      <td>P250 R-Dynamic S AWD</td>\n", "      <td>NaN</td>\n", "      <td>AWD</td>\n", "      <td>All-Wheel Drive</td>\n", "      <td>113.1 in</td>\n", "      <td>84.4 in</td>\n", "      <td>2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>SALCJ2FX6LH858128</td>\n", "      <td>38.1 in</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SUV / Crossover</td>\n", "      <td>NaN</td>\n", "      <td>San Juan</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>A</td>\n", "      <td>9-Speed Automatic Overdrive</td>\n", "      <td>t86759</td>\n", "      <td>S AWD</td>\n", "      <td>NaN</td>\n", "      <td>AWD</td>\n", "      <td>All-Wheel Drive</td>\n", "      <td>107.9 in</td>\n", "      <td>85.6 in</td>\n", "      <td>2020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>SALZL2GX4LH007593</td>\n", "      <td>33.8 in</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>SUV / Crossover</td>\n", "      <td>NaN</td>\n", "      <td>San Juan</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>A</td>\n", "      <td>9-Speed Automatic Overdrive</td>\n", "      <td>t85531</td>\n", "      <td>P300 R-Dynamic SE AWD</td>\n", "      <td>NaN</td>\n", "      <td>AWD</td>\n", "      <td>All-Wheel Drive</td>\n", "      <td>105.6 in</td>\n", "      <td>82.7 in</td>\n", "      <td>2020</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 66 columns</p>\n", "</div>"], "text/plain": ["                 vin back_legroom  bed bed_height bed_length        body_type  \\\n", "0  ZACNJABB5KPJ92081      35.1 in  NaN        NaN        NaN  SUV / Crossover   \n", "1  SALCJ2FX1LH858117      38.1 in  NaN        NaN        NaN  SUV / Crossover   \n", "2  JF1VA2M67G9829723      35.4 in  NaN        NaN        NaN            Sedan   \n", "3  SALRR2RV0L2433391      37.6 in  NaN        NaN        NaN  SUV / Crossover   \n", "4  SALCJ2FXXLH862327      38.1 in  NaN        NaN        NaN  SUV / Crossover   \n", "5  SALYK2EX1LA261711      37.1 in  NaN        NaN        NaN  SUV / Crossover   \n", "6  3MZBPABL6KM107908      35.1 in  NaN        NaN        NaN            Sedan   \n", "7  SALYK2EX5LA275434      37.1 in  NaN        NaN        NaN  SUV / Crossover   \n", "8  SALCJ2FX6LH858128      38.1 in  NaN        NaN        NaN  SUV / Crossover   \n", "9  SALZL2GX4LH007593      33.8 in  NaN        NaN        NaN  SUV / Crossover   \n", "\n", "  cabin      city  city_fuel_economy  combine_fuel_economy  ...  transmission  \\\n", "0   NaN   Bayamon                NaN                   NaN  ...             A   \n", "1   NaN  San Juan                NaN                   NaN  ...             A   \n", "2   NaN  Guaynabo               17.0                   NaN  ...             M   \n", "3   NaN  San Juan                NaN                   NaN  ...             A   \n", "4   NaN  San Juan                NaN                   NaN  ...             A   \n", "5   NaN  San Juan                NaN                   NaN  ...             A   \n", "6   NaN   Bayamon                NaN                   NaN  ...             A   \n", "7   NaN  San Juan                NaN                   NaN  ...             A   \n", "8   NaN  San Juan                NaN                   NaN  ...             A   \n", "9   NaN  San Juan                NaN                   NaN  ...             A   \n", "\n", "          transmission_display  trimId              trim_name  \\\n", "0  9-Speed Automatic Overdrive  t83804           Latitude FWD   \n", "1  9-Speed Automatic Overdrive  t86759                  S AWD   \n", "2               6-Speed Manual  t58994                   Base   \n", "3  8-Speed Automatic Overdrive  t86074             V6 HSE AWD   \n", "4  9-Speed Automatic Overdrive  t86759                  S AWD   \n", "5  8-Speed Automatic Overdrive  t85614   P250 R-Dynamic S AWD   \n", "6  6-Speed Automatic Overdrive  t85256              Sedan FWD   \n", "7  8-Speed Automatic Overdrive  t85614   P250 R-Dynamic S AWD   \n", "8  9-Speed Automatic Overdrive  t86759                  S AWD   \n", "9  9-Speed Automatic Overdrive  t85531  P300 R-Dynamic SE AWD   \n", "\n", "   vehicle_damage_category wheel_system wheel_system_display wheelbase  \\\n", "0                      NaN          FWD    Front-Wheel Drive  101.2 in   \n", "1                      NaN          AWD      All-Wheel Drive  107.9 in   \n", "2                      NaN          AWD      All-Wheel Drive  104.3 in   \n", "3                      NaN          AWD      All-Wheel Drive    115 in   \n", "4                      NaN          AWD      All-Wheel Drive  107.9 in   \n", "5                      NaN          AWD      All-Wheel Drive  113.1 in   \n", "6                      NaN          FWD    Front-Wheel Drive  107.3 in   \n", "7                      NaN          AWD      All-Wheel Drive  113.1 in   \n", "8                      NaN          AWD      All-Wheel Drive  107.9 in   \n", "9                      NaN          AWD      All-Wheel Drive  105.6 in   \n", "\n", "     width  year  \n", "0  79.6 in  2019  \n", "1  85.6 in  2020  \n", "2  78.9 in  2016  \n", "3  87.4 in  2020  \n", "4  85.6 in  2020  \n", "5  84.4 in  2020  \n", "6  70.7 in  2019  \n", "7  84.4 in  2020  \n", "8  85.6 in  2020  \n", "9  82.7 in  2020  \n", "\n", "[10 rows x 66 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["cars_df.head(10)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_rows', None)\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cleaning"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["is_certified               100.000000\n", "combine_fuel_economy       100.000000\n", "vehicle_damage_category    100.000000\n", "bed                         99.347742\n", "cabin                       97.882262\n", "is_oemcpo                   95.487993\n", "is_cpo                      93.903481\n", "bed_length                  85.696924\n", "bed_height                  85.696924\n", "owner_count                 50.566426\n", "salvage                     47.552533\n", "theft_title                 47.552533\n", "frame_damaged               47.552533\n", "fleet                       47.552533\n", "has_accidents               47.552533\n", "isCab                       47.552533\n", "franchise_make              19.087579\n", "torque                      17.259537\n", "city_fuel_economy           16.375948\n", "highway_fuel_economy        16.375948\n", "power                       16.047319\n", "interior_color              12.799363\n", "main_picture_url            12.302936\n", "major_options                6.668178\n", "engine_displacement          5.746123\n", "horsepower                   5.746123\n", "width                        5.308896\n", "back_legroom                 5.308896\n", "front_legroom                5.308896\n", "height                       5.308896\n", "fuel_tank_volume             5.308896\n", "length                       5.308896\n", "maximum_seating              5.308896\n", "wheelbase                    5.308896\n", "wheel_system_display         4.891001\n", "wheel_system                 4.891001\n", "mileage                      4.812836\n", "trim_name                    3.876415\n", "trimId                       3.860849\n", "engine_cylinders             3.352655\n", "engine_type                  3.352655\n", "fuel_type                    2.757430\n", "description                  2.596665\n", "transmission                 2.139471\n", "transmission_display         2.139471\n", "exterior_color               1.665144\n", "seller_rating                1.362382\n", "body_type                    0.451427\n", "sp_id                        0.003200\n", "vin                          0.000000\n", "is_new                       0.000000\n", "franchise_dealer             0.000000\n", "daysonmarket                 0.000000\n", "dealer_zip                   0.000000\n", "city                         0.000000\n", "listed_date                  0.000000\n", "longitude                    0.000000\n", "listing_id                   0.000000\n", "price                        0.000000\n", "model_name                   0.000000\n", "make_name                    0.000000\n", "latitude                     0.000000\n", "listing_color                0.000000\n", "sp_name                      0.000000\n", "savings_amount               0.000000\n", "year                         0.000000\n", "dtype: float64\n"]}], "source": ["missing_percentages = (cars_df.isnull().sum() / len(cars_df)) * 100\n", "sorted_missing_percentages = missing_percentages.sort_values(ascending=False)\n", "print(sorted_missing_percentages)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Dropping these columns as their missing values are extremely high"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columns dropped: ['bed', 'bed_height', 'bed_length', 'cabin', 'combine_fuel_economy', 'fleet', 'frame_damaged', 'has_accidents', 'isCab', 'is_certified', 'is_cpo', 'is_oemcpo', 'owner_count', 'salvage', 'theft_title', 'vehicle_damage_category']\n", "New shape of cars_df: (3000040, 50)\n"]}], "source": ["missing_threshold = 0.45\n", "missing_percentages = cars_df.isnull().sum() / len(cars_df)\n", "columns_to_drop = missing_percentages[missing_percentages >= missing_threshold].index\n", "cars_df.drop(columns=columns_to_drop, inplace=True)\n", "print(f\"Columns dropped: {list(columns_to_drop)}\")\n", "print(f\"New shape of cars_df: {cars_df.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We lost 16 columns"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3000040 entries, 0 to 3000039\n", "Data columns (total 50 columns):\n", " #   Column                Dtype   \n", "---  ------                -----   \n", " 0   vin                   object  \n", " 1   back_legroom          object  \n", " 2   body_type             category\n", " 3   city                  category\n", " 4   city_fuel_economy     float32 \n", " 5   daysonmarket          int16   \n", " 6   dealer_zip            object  \n", " 7   description           object  \n", " 8   engine_cylinders      category\n", " 9   engine_displacement   float32 \n", " 10  engine_type           object  \n", " 11  exterior_color        category\n", " 12  franchise_dealer      bool    \n", " 13  franchise_make        object  \n", " 14  front_legroom         object  \n", " 15  fuel_tank_volume      object  \n", " 16  fuel_type             object  \n", " 17  height                object  \n", " 18  highway_fuel_economy  float32 \n", " 19  horsepower            float32 \n", " 20  interior_color        object  \n", " 21  is_new                bool    \n", " 22  latitude              float32 \n", " 23  length                object  \n", " 24  listed_date           object  \n", " 25  listing_color         object  \n", " 26  listing_id            int64   \n", " 27  longitude             float32 \n", " 28  main_picture_url      object  \n", " 29  major_options         object  \n", " 30  make_name             object  \n", " 31  maximum_seating       object  \n", " 32  mileage               float32 \n", " 33  model_name            object  \n", " 34  power                 object  \n", " 35  price                 float32 \n", " 36  savings_amount        int64   \n", " 37  seller_rating         float32 \n", " 38  sp_id                 float32 \n", " 39  sp_name               object  \n", " 40  torque                object  \n", " 41  transmission          object  \n", " 42  transmission_display  object  \n", " 43  trimId                object  \n", " 44  trim_name             object  \n", " 45  wheel_system          object  \n", " 46  wheel_system_display  object  \n", " 47  wheelbase             object  \n", " 48  width                 object  \n", " 49  year                  int32   \n", "dtypes: bool(2), category(4), float32(10), int16(1), int32(1), int64(2), object(30)\n", "memory usage: 888.3+ MB\n"]}], "source": ["cars_df.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Train-test split"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We are going to do the split now to avoid data leakage during cleaning and feature engineering."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["X = cars_df.drop('price', axis=1) \n", "y = cars_df['price']"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["y_train_log = np.log1p(y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Cleaning II"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I'm going to analyze every column individually to make sure I'm not missing anything."]}, {"cell_type": "markdown", "metadata": {}, "source": ["-----------"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Feature Engineering"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Modeling"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### LightGBM"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### CatBoostRegressor"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Error-Modeling"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}